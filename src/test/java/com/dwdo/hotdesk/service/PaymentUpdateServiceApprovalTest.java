package com.dwdo.hotdesk.service;

import com.dwdo.hotdesk.dto.response.GeneralBodyResponse;
import com.dwdo.hotdesk.model.SubmissionPayment;
import com.dwdo.hotdesk.repository.PaymentSubmissionRepository;
import com.dwdo.hotdesk.repository.SubmissionPaymentRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("Payment Update Service Approval Tests")
class PaymentUpdateServiceApprovalTest {

    @Mock
    private PaymentSubmissionRepository submissionRepository;

    @Mock
    private SubmissionPaymentRepository submissionPaymentRepository;

    @Mock
    private ApprovalService approvalService;

    @InjectMocks
    private PaymentUpdateService paymentUpdateService;

    private static final String TEST_NIP = "123456789";
    private static final String TEST_TASK_NAME = "PAYMENT_UPDATE_TASK_123";

    @BeforeEach
    void setUp() {
        // Set up configuration values
        ReflectionTestUtils.setField(paymentUpdateService, "paymentUpdateRuleCode", "staggered_payment");
        ReflectionTestUtils.setField(paymentUpdateService, "paymentUpdateSystemCode", "arjuna");
        ReflectionTestUtils.setField(paymentUpdateService, "paymentUpdateProcessName", "Staggered Payment Update");
    }

    @Test
    @DisplayName("Should successfully initiate payment update approval")
    void shouldSuccessfullyInitiatePaymentUpdateApproval() {
        // Given
        when(approvalService.submitToApprovalWithConfig(anyString(), anyString(), anyString(), anyString(), anyString(), any()))
                .thenReturn(TEST_TASK_NAME);
        when(submissionPaymentRepository.findByReferenceNumber(TEST_NIP)).thenReturn(Optional.empty());
        when(submissionPaymentRepository.save(any(SubmissionPayment.class))).thenReturn(new SubmissionPayment());

        // When
        GeneralBodyResponse response = paymentUpdateService.initiatePaymentUpdateApproval(TEST_NIP);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("OK", response.getStatus());
        assertTrue(response.getMessage().contains("Payment update approval initiated successfully"));

        verify(approvalService).submitToApprovalWithConfig(anyString(), anyString(), anyString(), anyString(), anyString(), any());
        verify(approvalService).updateCurrentReviewerForTask(eq(TEST_TASK_NAME), anyString(), any());
        verify(submissionPaymentRepository).save(any(SubmissionPayment.class));
    }

    @Test
    @DisplayName("Should handle approval submission failure")
    void shouldHandleApprovalSubmissionFailure() {
        // Given
        when(approvalService.submitToApprovalWithConfig(anyString(), anyString(), anyString(), anyString(), anyString(), any()))
                .thenReturn(null);

        // When
        GeneralBodyResponse response = paymentUpdateService.initiatePaymentUpdateApproval(TEST_NIP);

        // Then
        assertNotNull(response);
        assertEquals(400, response.getCode());
        assertEquals("Error", response.getStatus());
        assertTrue(response.getMessage().contains("Failed to initiate payment update approval"));

        verify(approvalService).submitToApprovalWithConfig(anyString(), anyString(), anyString(), anyString(), anyString(), any());
        verify(submissionPaymentRepository, never()).save(any());
    }

    @Test
    @DisplayName("Should update existing payment approval record")
    void shouldUpdateExistingPaymentApprovalRecord() {
        // Given
        SubmissionPayment existingPayment = SubmissionPayment.builder()
                .referenceNumber(TEST_NIP)
                .taskName("OLD_TASK")
                .build();

        when(approvalService.submitToApprovalWithConfig(anyString(), anyString(), anyString(), anyString(), anyString(), any()))
                .thenReturn(TEST_TASK_NAME);
        when(submissionPaymentRepository.findByReferenceNumber(TEST_NIP)).thenReturn(Optional.of(existingPayment));
        when(submissionPaymentRepository.save(any(SubmissionPayment.class))).thenReturn(existingPayment);

        // When
        GeneralBodyResponse response = paymentUpdateService.initiatePaymentUpdateApproval(TEST_NIP);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("OK", response.getStatus());

        verify(submissionPaymentRepository).save(argThat(payment ->
                TEST_TASK_NAME.equals(payment.getTaskName()) &&
                TEST_NIP.equals(payment.getReferenceNumber())
        ));
        verify(approvalService).updateCurrentReviewerForTask(eq(TEST_TASK_NAME), anyString(), any());
    }
}

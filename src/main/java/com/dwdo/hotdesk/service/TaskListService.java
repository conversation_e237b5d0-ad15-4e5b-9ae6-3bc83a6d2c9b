package com.dwdo.hotdesk.service;

import com.dwdo.hotdesk.dto.TaskListDTO;
import com.dwdo.hotdesk.model.Submission;
import com.dwdo.hotdesk.model.SubmissionPayment;
import com.dwdo.hotdesk.repository.PaymentSubmissionRepository;
import com.dwdo.hotdesk.repository.SubmissionPaymentRepository;
import com.dwdo.hotdesk.security.SecurityUtil;
import com.dwdo.hotdesk.util.NullUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Slf4j
@Service
@RequiredArgsConstructor
public class TaskListService {

    private final PaymentSubmissionRepository submissionRepository;
    private final SubmissionFilterService filterService;

    public Page<TaskListDTO> getApprovalTaskList(
            Pageable pageable,
            String status,
            String startDate,
            String endDate,
            String nip,
            String name,
            String paymentType) {

        log.info("Getting approval task list for current user");

        String currentUserNik = SecurityUtil.getCurrentUserLogin()
                .orElseThrow(() -> new RuntimeException("Current user not found"));

        log.info("Current user NIK: {}", currentUserNik);

        LocalDateTime parsedStartDate = filterService.parseStartDate(startDate);
        LocalDateTime parsedEndDate = filterService.parseEndDate(endDate);

        Specification<Submission> spec = filterService.buildFilterSpecification(
                null,
                status,
                parsedStartDate,
                parsedEndDate,
                nip,
                name,
                paymentType
        );

        spec = spec.and((root, query, cb) -> cb.like(root.get("currentReviewer"), "%" + currentUserNik + "%"));

        spec = spec.and((root, query, cb) -> cb.notEqual(root.get("createdBy"), currentUserNik));

        Page<Submission> submissionsPage = submissionRepository.findAll(spec, pageable);

        log.info("Found {} approval tasks for user {} (excluding self-created submissions)", submissionsPage.getTotalElements(), currentUserNik);

        return submissionsPage.map(this::mapToTaskListDTO);
    }

    private TaskListDTO mapToTaskListDTO(Submission submission) {
        return TaskListDTO.builder()
                .id(submission.getId())
                .taskName(submission.getTaskName())
                .referenceNumber(submission.getReferenceNumber())
                .submitterName(submission.getSubmitterName())
                .submitterJob(submission.getSubmitterJob())
                .status(submission.getStatus())
                .nip(submission.getNip())
                .name(submission.getName())
                .grade(submission.getGrade())
                .paymentType(submission.getPaymentType())
                .amount(submission.getAmount())
                .description(submission.getDescription())
                .monthOfProcess(submission.getMonthOfProcess())
                .yearOfProcess(submission.getYearOfProcess())
                .directorate(submission.getDirectorate())
                .slik(submission.getSlik())
                .sanction(submission.getSanction())
                .terminationDate(submission.getTerminationDate())
                .eligible(submission.getEligible())
                .paymentDate(NullUtil.toDisplayString(submission.getPaymentDate()))
                .remarks(NullUtil.toDisplayString(submission.getRemarks()))
                .build();
    }


}

package com.dwdo.hotdesk.service;

import com.dwdo.hotdesk.dto.PaymentUpdateDTO;
import com.dwdo.hotdesk.dto.response.GeneralBodyResponse;
import com.dwdo.hotdesk.model.Submission;
import com.dwdo.hotdesk.model.SubmissionPayment;
import com.dwdo.hotdesk.repository.PaymentSubmissionRepository;
import com.dwdo.hotdesk.repository.SubmissionPaymentRepository;
import com.dwdo.hotdesk.restcontrolleradvice.CustomBadRequestException;
import com.dwdo.hotdesk.security.SecurityUtil;
import com.dwdo.hotdesk.service.feign.ApprovalClient;
import com.dwdo.hotdesk.service.feign.request.SubmitTransactionRequest;
import com.dwdo.hotdesk.service.feign.response.ApiResponse;
import com.dwdo.hotdesk.service.feign.response.DetailResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentUpdateService {

    private final PaymentSubmissionRepository submissionRepository;
    private final SubmissionPaymentRepository submissionPaymentRepository;
    private static final String EXCEL_TYPE_XLS = "application/vnd.ms-excel";
    private static final String EXCEL_TYPE_XLSX = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    private static final DateTimeFormatter PRIMARY_DATE_FORMATTER = DateTimeFormatter.ofPattern("dd-MMM-yyyy");
    private static final DateTimeFormatter ALTERNATE_DATE_FORMATTER = DateTimeFormatter.ofPattern("dd-MM-yyyy");

    public GeneralBodyResponse updatePaymentFromFile(MultipartFile file) {
        if (!isExcelFile(file)) {
            throw new CustomBadRequestException(400, "Invalid File", "Only Excel files are allowed");
        }

        try {
            log.info("[PaymentUpdateService] Processing payment update file: {}", file.getOriginalFilename());
            List<PaymentUpdateDTO> updates = processExcelFile(file);
            return updatePayments(updates);
        } catch (IOException e) {
            log.error("[PaymentUpdateService] Error processing Excel file", e);
            throw new CustomBadRequestException(400, "Processing Error", "Failed to process the Excel file");
        }
    }

    public GeneralBodyResponse updatePaymentFromJson(List<PaymentUpdateDTO> updates) {
        log.info("[PaymentUpdateService] Processing payment updates from JSON for {} records", updates.size());
        return updatePayments(updates);
    }

    private GeneralBodyResponse updatePayments(List<PaymentUpdateDTO> updates) {
        if (updates == null || updates.isEmpty()) {
            return GeneralBodyResponse.builder()
                    .code(400)
                    .status("Error")
                    .message("No payment updates provided")
                    .build();
        }

        UpdateResults results = processUpdates(updates);
        String message = buildResultMessage(results);

        return GeneralBodyResponse.builder()
                .code(results.successCount > 0 ? 200 : 400)
                .status(results.successCount > 0 ? "OK" : "Error")
                .message(message)
                .data(results.resultMap)
                .build();
    }

    private UpdateResults processUpdates(List<PaymentUpdateDTO> updates) {
        UpdateResults results = new UpdateResults();
        
        for (PaymentUpdateDTO update : updates) {
            try {
                if (!isValidUpdate(update, results)) {
                    continue;
                }
                
                updateSubmission(update, results);
            } catch (Exception e) {
                handleUpdateException(update, results, e);
            }
        }
        
        return results;
    }

    private boolean isValidUpdate(PaymentUpdateDTO update, UpdateResults results) {
        if (update.getReferenceNumber() == null || update.getReferenceNumber().isEmpty()) {
            results.resultMap.put("Row " + (results.resultMap.size() + 1), "Failed: Reference number is required");
            results.failCount++;
            return false;
        }
        
        Optional<Submission> submissionOpt = submissionRepository.findByReferenceNumber(update.getReferenceNumber());
        if (submissionOpt.isEmpty()) {
            results.resultMap.put(update.getReferenceNumber(), "Not Found: Submission with this reference number does not exist");
            results.notFoundCount++;
            log.warn("[PaymentUpdateService] Reference number not found: {}", update.getReferenceNumber());
            return false;
        }
        
        return true;
    }

    private void updateSubmission(PaymentUpdateDTO update, UpdateResults results) {
        Optional<Submission> submissionOpt = submissionRepository.findByReferenceNumber(update.getReferenceNumber());

        if (submissionOpt.isEmpty()) {
            log.warn("[PaymentUpdateService] Attempted to update non-existent submission: {}", update.getReferenceNumber());
            results.resultMap.put(update.getReferenceNumber(), "Not Found: Submission with this reference number does not exist");
            results.notFoundCount++;
            return;
        }

        Submission submission = submissionOpt.get();

        // Handle payment data in SubmissionPayment table
        if (update.getPaymentDate() != null) {
            // Create or update payment data
            createOrUpdatePaymentData(update.getReferenceNumber(), update.getPaymentDate());
            submission.setStatus("Paid");
            log.info("[PaymentUpdateService] Created/updated payment data and set status to Paid for reference: {}", update.getReferenceNumber());
        } else {
            // Remove payment data if it exists
            removePaymentData(update.getReferenceNumber());
            submission.setStatus("Unpaid");
            log.info("[PaymentUpdateService] Removed payment data and set status to Unpaid for reference: {}", update.getReferenceNumber());
        }

        submissionRepository.save(submission);
        results.resultMap.put(update.getReferenceNumber(), "Success");
        results.successCount++;
        log.info("[PaymentUpdateService] Successfully updated payment for reference: {}", update.getReferenceNumber());
    }

    private void createOrUpdatePaymentData(String referenceNumber, LocalDate paymentDate) {
        Optional<SubmissionPayment> existingPayment = submissionPaymentRepository.findByReferenceNumber(referenceNumber);

        if (existingPayment.isPresent()) {
            // Update existing payment data
            SubmissionPayment payment = existingPayment.get();
            payment.setPaymentDate(paymentDate);
            submissionPaymentRepository.save(payment);
            log.debug("[PaymentUpdateService] Updated existing payment data for reference: {}", referenceNumber);
        } else {
            // Create new payment data
            SubmissionPayment newPayment = SubmissionPayment.builder()
                    .referenceNumber(referenceNumber)
                    .paymentDate(paymentDate)
                    .taskName(null) // Different from submission task_name as per requirement
                    .currentReviewer(null) // Different from submission current_reviewer as per requirement
                    .build();
            submissionPaymentRepository.save(newPayment);
            log.debug("[PaymentUpdateService] Created new payment data for reference: {}", referenceNumber);
        }
    }

    private void removePaymentData(String referenceNumber) {
        Optional<SubmissionPayment> existingPayment = submissionPaymentRepository.findByReferenceNumber(referenceNumber);
        if (existingPayment.isPresent()) {
            submissionPaymentRepository.delete(existingPayment.get());
            log.debug("[PaymentUpdateService] Removed payment data for reference: {}", referenceNumber);
        }
    }

    private void handleUpdateException(PaymentUpdateDTO update, UpdateResults results, Exception e) {
        String reference = update.getReferenceNumber() != null ? update.getReferenceNumber() : "Unknown";
        results.resultMap.put(reference, "Failed: " + e.getMessage());
        results.failCount++;
        log.error("[PaymentUpdateService] Failed to update payment for reference: {}", reference, e);
    }

    private String buildResultMessage(UpdateResults results) {
        if (results.notFoundCount > 0) {
            if (results.successCount > 0) {
                return String.format("Payment updates processed: %d successful, %d not found, %d failed", 
                        results.successCount, results.notFoundCount, results.failCount);
            } else {
                return String.format("No payments were updated: %d not found, %d failed", 
                        results.notFoundCount, results.failCount);
            }
        } else {
            return String.format("Payment updates processed: %d successful, %d failed", 
                    results.successCount, results.failCount);
        }
    }

    private static class UpdateResults {
        Map<String, String> resultMap = new HashMap<>();
        int successCount = 0;
        int failCount = 0;
        int notFoundCount = 0;
    }

    private boolean isExcelFile(MultipartFile file) {
        String contentType = file.getContentType();
        return contentType != null && (
                contentType.equals(EXCEL_TYPE_XLS) ||
                contentType.equals(EXCEL_TYPE_XLSX)
        );
    }

    private List<PaymentUpdateDTO> processExcelFile(MultipartFile file) throws IOException {
        List<PaymentUpdateDTO> updates = new ArrayList<>();
        
        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row != null && isValidRow(row, i)) {
                    updates.add(createPaymentUpdateFromRow(row, i));
                }
            }
        }
        
        return updates;
    }

    private boolean isValidRow(Row row, int rowIndex) {
        String referenceNumber = getStringCellValue(row.getCell(0));
        
        if (referenceNumber == null || referenceNumber.isEmpty()) {
            log.warn("[PaymentUpdateService] Skipping row {} - missing reference number", rowIndex + 1);
            return false;
        }
        
        return true;
    }

    private PaymentUpdateDTO createPaymentUpdateFromRow(Row row, int rowIndex) {
        String referenceNumber = getStringCellValue(row.getCell(0));
        String paymentDateStr = getStringCellValue(row.getCell(1));
        
        LocalDate paymentDate = null;
        if (paymentDateStr != null && !paymentDateStr.isEmpty()) {
            try {
                paymentDate = parsePaymentDate(paymentDateStr);
            } catch (CustomBadRequestException e) {
                log.warn("[PaymentUpdateService] Invalid date format in row {}: {}", rowIndex + 1, paymentDateStr);
                throw new CustomBadRequestException(400, "Invalid Date", 
                        "Invalid date format in row " + (rowIndex + 1) + ": " + paymentDateStr + 
                        ". Expected formats: dd-MMM-yyyy or dd-MM-yyyy");
            }
        }
        
        return new PaymentUpdateDTO(referenceNumber, paymentDate);
    }

    private String getStringCellValue(Cell cell) {
        if (cell == null) return null;

        return switch (cell.getCellType()) {
            case STRING -> cell.getStringCellValue();
            case NUMERIC -> {
                if (DateUtil.isCellDateFormatted(cell)) {
                    yield cell.getLocalDateTimeCellValue().toLocalDate().toString();
                } else {
                    yield String.valueOf((long) cell.getNumericCellValue());
                }
            }
            case BOOLEAN -> String.valueOf(cell.getBooleanCellValue());
            default -> null;
        };
    }

    private LocalDate parsePaymentDate(String dateStr) {
        if (dateStr == null || dateStr.isEmpty()) {
            return null;
        }
        
        try {
            return LocalDate.parse(dateStr, PRIMARY_DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            try {
                return LocalDate.parse(dateStr, ALTERNATE_DATE_FORMATTER);
            } catch (DateTimeParseException ex) {
                throw new CustomBadRequestException(400, "Invalid Date", 
                        "Invalid date format: " + dateStr + ". Expected formats: dd-MMM-yyyy or dd-MM-yyyy");
            }
        }
    }
}

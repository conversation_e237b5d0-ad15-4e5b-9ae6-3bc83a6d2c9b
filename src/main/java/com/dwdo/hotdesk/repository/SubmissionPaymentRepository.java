package com.dwdo.hotdesk.repository;

import com.dwdo.hotdesk.model.SubmissionPayment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface SubmissionPaymentRepository extends JpaRepository<SubmissionPayment, Long>, JpaSpecificationExecutor<SubmissionPayment> {
    
    /**
     * Find SubmissionPayment by reference number
     */
    Optional<SubmissionPayment> findByReferenceNumber(String referenceNumber);
    
    /**
     * Find SubmissionPayment by task name
     */
    Optional<SubmissionPayment> findByTaskName(String taskName);
    
    /**
     * Find SubmissionPayments by task names
     */
    List<SubmissionPayment> findByTaskNameIn(List<String> taskNames);
    
    /**
     * Check if SubmissionPayment exists by reference number
     */
    boolean existsByReferenceNumber(String referenceNumber);
    
    /**
     * Find SubmissionPayments with payment date
     */
    List<SubmissionPayment> findByPaymentDateIsNotNull();
    
    /**
     * Find SubmissionPayments without payment date
     */
    List<SubmissionPayment> findByPaymentDateIsNull();
    
    /**
     * Find SubmissionPayments by payment date
     */
    List<SubmissionPayment> findByPaymentDate(LocalDate paymentDate);
    
    /**
     * Find SubmissionPayments by payment date range
     */
    List<SubmissionPayment> findByPaymentDateBetween(LocalDate startDate, LocalDate endDate);
    
    /**
     * Find SubmissionPayments by current reviewer
     */
    List<SubmissionPayment> findByCurrentReviewer(String currentReviewer);
    
    /**
     * Find SubmissionPayments by current reviewer containing text
     */
    List<SubmissionPayment> findByCurrentReviewerContaining(String reviewerText);
    
    /**
     * Count SubmissionPayments by reference number
     */
    long countByReferenceNumber(String referenceNumber);
    
    /**
     * Find SubmissionPayment with submission details
     */
    @Query("SELECT sp FROM SubmissionPayment sp LEFT JOIN FETCH sp.submission WHERE sp.referenceNumber = :referenceNumber")
    Optional<SubmissionPayment> findByReferenceNumberWithSubmission(@Param("referenceNumber") String referenceNumber);
    
    /**
     * Find all SubmissionPayments with submission details
     */
    @Query("SELECT sp FROM SubmissionPayment sp LEFT JOIN FETCH sp.submission")
    List<SubmissionPayment> findAllWithSubmission();
    
    /**
     * Find SubmissionPayments by task name with submission details
     */
    @Query("SELECT sp FROM SubmissionPayment sp LEFT JOIN FETCH sp.submission WHERE sp.taskName = :taskName")
    List<SubmissionPayment> findByTaskNameWithSubmission(@Param("taskName") String taskName);
    
    /**
     * Find SubmissionPayments that have payment date but submission status is not 'Paid'
     */
    @Query("SELECT sp FROM SubmissionPayment sp JOIN sp.submission s WHERE sp.paymentDate IS NOT NULL AND s.status != 'Paid'")
    List<SubmissionPayment> findPaidButStatusNotUpdated();
    
    /**
     * Find SubmissionPayments that don't have payment date but submission status is 'Paid'
     */
    @Query("SELECT sp FROM SubmissionPayment sp JOIN sp.submission s WHERE sp.paymentDate IS NULL AND s.status = 'Paid'")
    List<SubmissionPayment> findUnpaidButStatusPaid();
}

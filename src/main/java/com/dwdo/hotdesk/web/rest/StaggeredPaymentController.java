package com.dwdo.hotdesk.web.rest;

import com.dwdo.hotdesk.dto.PaymentUpdateDTO;
import com.dwdo.hotdesk.dto.TaskListDTO;
import com.dwdo.hotdesk.dto.request.SingleValidRequestDTO;
import com.dwdo.hotdesk.dto.request.GeneratePaymentRequestDTO;
import com.dwdo.hotdesk.dto.request.BulkActionRequestDTO;
import com.dwdo.hotdesk.dto.request.ReportRequestDTO;
import com.dwdo.hotdesk.dto.request.PaymentUpdateApprovalRequestDTO;
import com.dwdo.hotdesk.dto.SubmissionHistoryDTO;
import com.dwdo.hotdesk.dto.SubmissionDetailDTO;
import com.dwdo.hotdesk.dto.response.GeneralBodyResponse;
import com.dwdo.hotdesk.restcontrolleradvice.CustomBadRequestException;
import com.dwdo.hotdesk.service.*;
import com.dwdo.hotdesk.service.feign.response.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;

import java.util.List;

import com.dwdo.hotdesk.dto.FileDTO;
import com.dwdo.hotdesk.model.Submission;
import com.dwdo.hotdesk.model.SupportingFiles;
import com.dwdo.hotdesk.repository.PaymentSubmissionRepository;
import com.dwdo.hotdesk.service.storage.CloudStorageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;

@Slf4j
@RestController
@RequestMapping("/api/staggered")
@RequiredArgsConstructor
public class StaggeredPaymentController {

    private final PaymentValidationService validationService;
    private final FileTemplateService fileTemplateService;
    private final SubmissionService submissionService;
    private final SubmissionHistoryService submissionHistoryService;
    private final SubmissionDetailService submissionDetailService;
    private final PaymentUpdateService paymentUpdateService;
    private final TaskListService taskListService;
    private final GeneratePaymentService generatePaymentService;
    private final ApprovalService approvalService;
    private final ReportService reportService;

    @Autowired
    private PaymentSubmissionRepository submissionRepository;

    @Autowired
    @Qualifier("staggeredStorageService")
    private CloudStorageService storageService;

    @PostMapping(value = "/bulk-validate",
            consumes = MediaType.MULTIPART_FORM_DATA_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<GeneralBodyResponse> validateExcelFile(@RequestParam("file") MultipartFile file) {
        log.info("[BulkValidate] Received file validation request: {}", file.getOriginalFilename());
        GeneralBodyResponse response = validationService.validateExcelFile(file);

        log.info("[BulkValidate] Response built: code={}, status={}, message={}",
                response.getCode(), response.getStatus(), response.getMessage());
        log.info("[BulkValidate] Data type: {}", response.getData() != null ? response.getData().getClass().getName() : "null");
        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_JSON)
                .body(response);
    }

    @PostMapping(value = "/single-validate",
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<GeneralBodyResponse> validateSinglePayment(@Valid @RequestBody SingleValidRequestDTO request) {
        log.info("[SingleValidate] Received single payment validation request for NIP: {}", request.getNip());
        GeneralBodyResponse response = validationService.validateSinglePayment(request);

        log.info("[SingleValidate] Response built: code={}, status={}, message={}",
                response.getCode(), response.getStatus(), response.getMessage());
        log.info("[SingleValidate] Data type: {}", response.getData() != null ? response.getData().getClass().getName() : "null");
        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_JSON)
                .body(response);
    }

    @GetMapping("/template-submission")
    public ResponseEntity<Resource> getTemplateFile() {
        log.info("[TemplateFile] Request received for staggered payment template file");
        return fileTemplateService.generateStaggeredPaymentTemplate();
    }

    @GetMapping("/template-payment")
    public ResponseEntity<Resource> getPaymentTemplate() {
        log.info("[TemplatePayment] Request received for payment template file");
        return fileTemplateService.generatePaymentTemplate();
    }

    @PostMapping(value = "/bulk-submission",
            consumes = MediaType.MULTIPART_FORM_DATA_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<List<GeneralBodyResponse>> submitPayment(
            @RequestParam("mainFile") MultipartFile mainFile,
            @RequestParam(value = "supportingFiles", required = false) List<MultipartFile> supportingFiles) {

        log.info("[BulkSubmission] Received bulk submission request for processing");

        List<GeneralBodyResponse> responses = submissionService.bulkSubmission(
                mainFile, supportingFiles);

        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_JSON)
                .body(responses);
    }

    @PostMapping(value = "/single-submission",
            consumes = MediaType.MULTIPART_FORM_DATA_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<GeneralBodyResponse> submitSinglePayment(
            @RequestPart("data") SingleValidRequestDTO requestData,
            @RequestPart(value = "supportingFiles", required = false) List<MultipartFile> supportingFiles) {

        log.info("[SingleSubmission] Received single submission request for payment: {}", requestData.getNip());
        
        GeneralBodyResponse response = submissionService.singleSubmission(
                requestData, supportingFiles);

        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_JSON)
                .body(response);
    }

    @GetMapping(value = "/submission/history", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<GeneralBodyResponse> getSubmissionHistory(
            @PageableDefault(page = 0, size = 10, sort = "id", direction = Sort.Direction.DESC) Pageable pageable,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "false") boolean currentUserOnly,
            @RequestParam(required = false) String nip,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String paymentType) {

        log.info("[SubmissionHistory] Received request: page={}, size={}, sort={}, status={}, dateRange={} to {}, currentUserOnly={}, nip={}, name={}, paymentType={}",
                pageable.getPageNumber(), pageable.getPageSize(), pageable.getSort(), status, startDate, endDate, currentUserOnly, nip, name, paymentType);

        Page<SubmissionHistoryDTO> submissionsPage = submissionHistoryService.getSubmissionHistoryWithApprovalDetails(
                pageable, status, startDate, endDate, currentUserOnly, nip, name, paymentType);

        GeneralBodyResponse response = GeneralBodyResponse.builder()
                .code(200)
                .status("OK")
                .message("Submissions retrieved successfully")
                .data(submissionsPage)
                .build();

        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_JSON)
                .body(response);
    }

    @GetMapping(value = "/submission/history/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<GeneralBodyResponse> getSubmissionDetail(@PathVariable Long id) {
        log.info("[SubmissionDetail] Received request for submission ID: {}", id);

        SubmissionDetailDTO submissionDetail = submissionDetailService.getSubmissionDetail(id);

        GeneralBodyResponse response = GeneralBodyResponse.builder()
                .code(200)
                .status("OK")
                .message("Submission detail retrieved successfully")
                .data(submissionDetail)
                .build();

        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_JSON)
                .body(response);
    }

    @GetMapping(value = "/log-{taskname}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<GeneralBodyResponse> getTransactionConsoleLog(@PathVariable String taskname) {
        log.info("[TransactionConsoleLog] Received request for task name: {}", taskname);

        ApiResponse logResponse = submissionDetailService.getTransactionConsoleLog(taskname);

        GeneralBodyResponse response = GeneralBodyResponse.builder()
                .code(200)
                .status("OK")
                .message("Transaction console log retrieved successfully")
                .data(logResponse.getData())
                .build();

        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_JSON)
                .body(response);
    }

    @GetMapping(value = "/list-approval", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<GeneralBodyResponse> getApprovalTaskList(
            @PageableDefault(page = 0, size = 10, sort = "id", direction = Sort.Direction.DESC) Pageable pageable,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String nip,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String paymentType) {

        log.info("[ApprovalTaskList] Received request: page={}, size={}, sort={}, status={}, dateRange={} to {}, nip={}, name={}, paymentType={}",
                pageable.getPageNumber(), pageable.getPageSize(), pageable.getSort(), status, startDate, endDate, nip, name, paymentType);

        Page<TaskListDTO> tasksPage = taskListService.getApprovalTaskList(
                pageable, status, startDate, endDate, nip, name, paymentType);

        GeneralBodyResponse response = GeneralBodyResponse.builder()
                .code(200)
                .status("OK")
                .message("Approval tasks retrieved successfully")
                .data(tasksPage)
                .build();

        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_JSON)
                .body(response);
    }

    @GetMapping("/download/{submissionId}/supporting/{fileId}")
    public ResponseEntity<Resource> downloadSupportingFile(
            @PathVariable Long submissionId,
            @PathVariable Long fileId) {

        log.info("[FileDownload] Received request to download supporting file ID: {} for submission ID: {}", fileId, submissionId);

        try {
            Submission submission = submissionRepository.findByIdWithSupportingFiles(submissionId)
                    .orElseThrow(() -> new RuntimeException("Submission not found"));

            SupportingFiles supportingFile = submission.getSupportingFiles().stream()
                    .filter(file -> file.getId().equals(fileId))
                    .findFirst()
                    .orElseThrow(() -> new RuntimeException("Supporting file not found"));

            FileDTO fileDTO = storageService.loadFile(supportingFile.getFilePath());
            ByteArrayResource resource = new ByteArrayResource(fileDTO.getFile());

            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .header(HttpHeaders.CONTENT_DISPOSITION,
                            "attachment; filename=\"" + supportingFile.getFileName() + "\"")
                    .body(resource);
        } catch (Exception e) {
            log.error("[FileDownload] Error downloading supporting file ID: {} for submission ID: {}", fileId, submissionId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PostMapping(value = "/bulk-payment",
            consumes = MediaType.MULTIPART_FORM_DATA_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<GeneralBodyResponse> updatePaymentFromFile(@RequestParam("file") MultipartFile file) {
        log.info("[BulkPayment] Received payment update file: {}", file.getOriginalFilename());
        GeneralBodyResponse response = paymentUpdateService.updatePaymentFromFile(file);
        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_JSON)
                .body(response);
    }

    @PostMapping(value = "/single-payment",
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<GeneralBodyResponse> updatePaymentFromJson(@RequestBody List<PaymentUpdateDTO> updates) {
        log.info("[SinglePayment] Received payment update JSON for {} records", updates.size());
        GeneralBodyResponse response = paymentUpdateService.updatePaymentFromJson(updates);
        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_JSON)
                .body(response);
    }

    @PostMapping(value = "/generate-payment",
            consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Object> generatePaymentExcel(@Valid @RequestBody GeneratePaymentRequestDTO request) {
        log.info("[GeneratePayment] Received request to generate Excel for submissions with status 'Waiting for Payment', year: {}, month: {}",
                request.getYearOfProcess(), request.getMonthOfProcess());

        try {
            ResponseEntity<Resource> resourceResponse = generatePaymentService.generatePaymentExcel(request);
            return ResponseEntity.status(resourceResponse.getStatusCode())
                    .headers(resourceResponse.getHeaders())
                    .body(resourceResponse.getBody());
        } catch (CustomBadRequestException e) {
            log.error("[GeneratePayment] Validation error: {}", e.getMessage());
            GeneralBodyResponse errorResponse = GeneralBodyResponse.builder()
                    .code(e.getCode())
                    .status(e.getStatus())
                    .message(e.getMessage())
                    .build();
            return ResponseEntity.status(e.getCode())
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(errorResponse);
        } catch (Exception e) {
            log.error("[GeneratePayment] Unexpected error: {}", e.getMessage(), e);
            GeneralBodyResponse errorResponse = GeneralBodyResponse.builder()
                    .code(500)
                    .status("Internal Server Error")
                    .message("Failed to generate payment Excel: " + e.getMessage())
                    .build();
            return ResponseEntity.status(500)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(errorResponse);
        }
    }

    @PostMapping(value = "/action",
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<GeneralBodyResponse> processAction(@Valid @RequestBody BulkActionRequestDTO request) {
        log.info("[Action] Received action request: action={}, taskNames={}, reason={}, monthOfProcess={}, yearOfProcess={}, paymentType={}, amount={}",
                request.getAction(), request.getTaskNames(), request.getReason(),
                request.getMonthOfProcess(), request.getYearOfProcess(), request.getPaymentType(), request.getAmount());

        try {
            approvalService.processAction(request.getTaskNames(), request.getAction(), request.getReason(),
                    request.getMonthOfProcess(), request.getYearOfProcess(), request.getPaymentType(), request.getAmount());

            GeneralBodyResponse response = GeneralBodyResponse.builder()
                    .code(202)
                    .status("ACCEPTED")
                    .message("Action request accepted. Processing " + request.getTaskNames().size() +
                            " task(s) with action '" + request.getAction() + "' in the background. " +
                            "If any task fails, all changes will be rolled back.")
                    .build();

            log.info("[Action] Action request accepted for async transactional processing: taskCount={}, action={}",
                    request.getTaskNames().size(), request.getAction());

            return ResponseEntity.accepted()
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(response);

        } catch (Exception e) {
            log.error("[Action] Error starting async transactional processing", e);
            GeneralBodyResponse errorResponse = GeneralBodyResponse.builder()
                    .code(500)
                    .status("ERROR")
                    .message("Failed to start processing: " + e.getMessage())
                    .build();
            return ResponseEntity.status(500)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(errorResponse);
        }
    }

    @PostMapping(value = "/report",
            consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Object> generateReport(@Valid @RequestBody ReportRequestDTO request) {
        log.info("[Report] Received request to generate Excel report with filters: startDate={}, endDate={}, status={}",
                request.getStartDate(), request.getEndDate(), request.getStatus());

        try {
            ResponseEntity<Resource> resourceResponse = reportService.generateReportExcel(request);
            return ResponseEntity.status(resourceResponse.getStatusCode())
                    .headers(resourceResponse.getHeaders())
                    .body(resourceResponse.getBody());
        } catch (CustomBadRequestException e) {
            log.error("[Report] Validation error: {}", e.getMessage());
            GeneralBodyResponse errorResponse = GeneralBodyResponse.builder()
                    .code(e.getCode())
                    .status(e.getStatus())
                    .message(e.getMessage())
                    .build();
            return ResponseEntity.status(e.getCode())
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(errorResponse);
        } catch (Exception e) {
            log.error("[Report] Unexpected error: {}", e.getMessage(), e);
            GeneralBodyResponse errorResponse = GeneralBodyResponse.builder()
                    .code(500)
                    .status("Internal Server Error")
                    .message("Failed to generate report Excel: " + e.getMessage())
                    .build();
            return ResponseEntity.status(500)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(errorResponse);
        }
    }
}

# Payment Update Approval API

## Overview
This document describes the new Payment Update Approval API endpoint that allows initiating approval processes for payment updates.

## Configuration
The following configuration values are used for the payment update approval process:

```properties
rule.code.payment-update=staggered_payment
system.code.payment-update=arjuna
process.name.payment-updatet=Staggered Payment Update
```

## API Endpoint

### Initiate Payment Update Approval

**Endpoint:** `POST /api/staggered/payment-update-approval`

**Content-Type:** `application/json`

**Description:** Initiates an approval process for payment updates. This endpoint creates a new approval workflow for a specific NIP (employee ID).

**Request Body:**
```json
{
  "nip": "123456789"
}
```

**Request Body Validation:**
| Field | Type   | Required | Description                    |
|-------|--------|----------|--------------------------------|
| `nip` | String | Yes      | Employee NIP (ID) for approval |

**Success Response:**
- **Status Code:** 200 OK
- **Content-Type:** `application/json`

```json
{
  "code": 200,
  "status": "OK",
  "message": "Payment update approval initiated successfully for NIP: 123456789",
  "data": {
    "taskName": "PAYMENT_UPDATE_TASK_ABC123",
    "nip": "123456789"
  }
}
```

**Error Responses:**

| Status Code | Description                    | Response Body Example                                                                                    |
|-------------|--------------------------------|----------------------------------------------------------------------------------------------------------|
| 400         | Bad Request - Validation Error | `{"code": 400, "status": "Error", "message": "Failed to initiate payment update approval for NIP: 123456789"}` |
| 401         | Unauthorized                   | `{"code": 401, "status": "Authentication Error", "message": "User not authenticated or NIP not available"}` |
| 500         | Internal Server Error          | `{"code": 500, "status": "Error", "message": "Internal error occurred while initiating payment update approval: ..."}` |

## Process Flow

1. **Authentication Check**: Verifies that the current user is authenticated
2. **Approval Submission**: Uses `ApprovalService.submitToApprovalWithConfig()` to submit the payment update request with:
   - Rule Code: `staggered_payment`
   - System Code: `arjuna`
   - Process Name: `Staggered Payment Update`
   - Parameter: Only `nip` (no `isPayroll` parameter needed)
3. **Task Creation**: Creates or updates a `SubmissionPayment` record with:
   - Reference Number: The provided NIP
   - Task Name: Returned from approval service
   - Current Reviewer: Uses `ApprovalService.updateCurrentReviewerForTask()` to extract approver information
4. **Response**: Returns success/failure status with task details

## Code Reusability

This implementation reuses existing methods from `ApprovalService`:
- `submitToApprovalWithConfig()` - Handles approval submission with custom configuration
- `updateCurrentReviewerForTask()` - Updates current reviewer information using a functional approach

This eliminates code duplication and ensures consistency across all approval processes.

## Database Impact

The endpoint creates or updates records in the `submission_payment` table with:
- `reference_number`: The NIP provided in the request
- `task_name`: Task name returned from the approval service
- `current_reviewer`: JSON string containing approver information
- `payment_date`: Remains null until actual payment is processed

## Approval Information Structure

The `current_reviewer` field contains a JSON array with approver details:

```json
[
  {
    "approverCode": "APPROVER_001",
    "approverNik": "987654321",
    "approverName": "John Doe",
    "approverJob": "Manager"
  }
]
```

## Usage Example

```bash
curl -X POST http://localhost:8093/api/staggered/payment-update-approval \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "nip": "123456789"
  }'
```

## Notes

- This endpoint only initiates the approval process
- Actual payment updates still need to go through the existing payment update endpoints after approval
- The approval process follows the same pattern as the main staggered payment approval but with different configuration values
- No `isPayroll` parameter is required for payment update approvals
